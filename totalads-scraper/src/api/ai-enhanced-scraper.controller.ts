/**
 * AI-Enhanced Scraper Controller
 * REST API endpoints for AI-powered web scraping
 */

import { Request, Response } from 'express';
import { z } from 'zod';
import { aiEnhancedScraperService, AIEnhancedScrapeResult } from '../core/ai-enhanced-scraper.service';
import { handleError } from '../utils/error-handler';
import logger from '../utils/logger';

// Request validation schemas
const scrapeRequestSchema = z.object({
  url: z.string().url('Invalid URL format'),
  options: z.object({
    followRedirects: z.boolean().optional().default(true),
    maxDepth: z.number().min(1).max(5).optional().default(1),
    timeout: z.number().min(1000).max(60000).optional().default(30000),
    enableAI: z.boolean().optional().default(true),
    aiOptions: z.object({
      maxCost: z.number().min(0).max(1).optional().default(0.10), // Max $0.10 per request
      useCache: z.boolean().optional().default(true),
      cacheExpiryMinutes: z.number().min(1).max(1440).optional().default(60)
    }).optional()
  }).optional().default({})
});

const batchScrapeRequestSchema = z.object({
  urls: z.array(z.string().url()).min(1).max(10), // Limit to 10 URLs per batch
  options: z.object({
    followRedirects: z.boolean().optional().default(true),
    maxDepth: z.number().min(1).max(5).optional().default(1),
    timeout: z.number().min(1000).max(60000).optional().default(30000),
    enableAI: z.boolean().optional().default(true),
    aiOptions: z.object({
      maxCostPerUrl: z.number().min(0).max(1).optional().default(0.10),
      maxTotalCost: z.number().min(0).max(5).optional().default(1.00),
      useCache: z.boolean().optional().default(true),
      cacheExpiryMinutes: z.number().min(1).max(1440).optional().default(60)
    }).optional()
  }).optional().default({})
});

const budgetUpdateSchema = z.object({
  daily: z.number().min(0).max(100).optional(),
  monthly: z.number().min(0).max(1000).optional(),
  perRequest: z.number().min(0).max(1).optional()
});

/**
 * AI-Enhanced Scraper Controller
 */
export class AIEnhancedScraperController {
  
  /**
   * Scrape a single URL with AI enhancement
   */
  async scrapeWithAI(req: Request, res: Response): Promise<void> {
    try {
      logger.info('AI-enhanced scrape request received', { 
        url: req.body.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip 
      });

      // Validate request
      const validatedData = scrapeRequestSchema.parse(req.body);
      const { url, options } = validatedData;

      // Create browser task
      const task = {
        url,
        taskId: `ai-scrape-${Date.now()}`,
        options: {
          followRedirects: options.followRedirects,
          maxDepth: options.maxDepth,
          timeout: options.timeout
        }
      };

      // Set AI budget if specified
      if (options.aiOptions?.maxCost) {
        aiEnhancedScraperService.setAIBudget({
          perRequest: options.aiOptions.maxCost
        });
      }

      // Execute AI-enhanced scraping
      const result = await aiEnhancedScraperService.scrapeWithAI(task);

      // Prepare response
      const response = {
        success: true,
        data: {
          url: result.url,
          timestamp: result.timestamp,
          processingTime: result.processingTime,
          aiProcessingTime: result.aiProcessingTime,
          status: result.status,
          
          // Basic scraping results
          title: result.title,
          description: result.desc,
          links: result.nestedLinks,
          content: result.text,
          
          // AI-enhanced results
          businessIntelligence: result.businessIntelligence,
          enhancedContactInfo: result.enhancedContactInfo,
          extractedEntities: result.extractedEntities,
          sentiment: result.sentiment,
          
          // Cost and performance metrics
          aiCost: result.aiCost,
          recommendations: result.recommendations,
          
          // Traditional contact details for backward compatibility
          contactDetails: result.contactDetails,
          aboutData: result.aboutData
        },
        meta: {
          aiEnabled: true,
          costStats: aiEnhancedScraperService.getCostStats(),
          processingTime: result.processingTime,
          aiProcessingTime: result.aiProcessingTime
        }
      };

      logger.info('AI-enhanced scrape completed successfully', {
        url,
        status: result.status,
        aiCost: result.aiCost,
        confidence: result.businessIntelligence.confidence
      });

      res.status(200).json(response);

    } catch (error) {
      logger.error('AI-enhanced scrape failed', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        url: req.body?.url
      });

      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.errors
        });
        return;
      }

      const errorResponse = {
        success: false,
        error: 'Scraping failed',
        message: (error as Error).message,
        meta: {
          aiEnabled: true,
          costStats: aiEnhancedScraperService.getCostStats()
        }
      };

      res.status(500).json(errorResponse);
    }
  }

  /**
   * Batch scrape multiple URLs with AI enhancement
   */
  async batchScrapeWithAI(req: Request, res: Response): Promise<void> {
    try {
      logger.info('AI-enhanced batch scrape request received', { 
        urlCount: req.body.urls?.length,
        userAgent: req.get('User-Agent'),
        ip: req.ip 
      });

      // Validate request
      const validatedData = batchScrapeRequestSchema.parse(req.body);
      const { urls, options } = validatedData;

      // Set AI budget if specified
      if (options.aiOptions?.maxCostPerUrl) {
        aiEnhancedScraperService.setAIBudget({
          perRequest: options.aiOptions.maxCostPerUrl
        });
      }

      const results: AIEnhancedScrapeResult[] = [];
      const errors: { url: string; error: string }[] = [];
      let totalCost = 0;

      // Process URLs sequentially to control costs and avoid rate limits
      for (const url of urls) {
        try {
          // Check total cost limit
          if (options.aiOptions?.maxTotalCost && totalCost >= options.aiOptions.maxTotalCost) {
            errors.push({
              url,
              error: `Skipped due to total cost limit ($${options.aiOptions.maxTotalCost})`
            });
            continue;
          }

          const task = {
            url,
            taskId: `ai-batch-scrape-${Date.now()}-${Math.random()}`,
            options: {
              followRedirects: options.followRedirects,
              maxDepth: options.maxDepth,
              timeout: options.timeout
            }
          };

          const result = await aiEnhancedScraperService.scrapeWithAI(task);
          results.push(result);
          totalCost += result.aiCost;

          // Add small delay between requests to be respectful
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          logger.error(`Batch scrape failed for URL: ${url}`, error);
          errors.push({
            url,
            error: (error as Error).message
          });
        }
      }

      const response = {
        success: true,
        data: {
          results,
          summary: {
            totalUrls: urls.length,
            successfulScrapes: results.length,
            failedScrapes: errors.length,
            totalCost,
            averageCost: results.length > 0 ? totalCost / results.length : 0,
            totalProcessingTime: results.reduce((sum, r) => sum + r.processingTime, 0),
            totalAIProcessingTime: results.reduce((sum, r) => sum + r.aiProcessingTime, 0)
          },
          errors: errors.length > 0 ? errors : undefined
        },
        meta: {
          aiEnabled: true,
          costStats: aiEnhancedScraperService.getCostStats()
        }
      };

      logger.info('AI-enhanced batch scrape completed', {
        totalUrls: urls.length,
        successful: results.length,
        failed: errors.length,
        totalCost
      });

      res.status(200).json(response);

    } catch (error) {
      logger.error('AI-enhanced batch scrape failed', error);

      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.errors
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: 'Batch scraping failed',
        message: (error as Error).message,
        meta: {
          aiEnabled: true,
          costStats: aiEnhancedScraperService.getCostStats()
        }
      });
    }
  }

  /**
   * Get AI cost statistics
   */
  async getCostStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = aiEnhancedScraperService.getCostStats();
      
      res.status(200).json({
        success: true,
        data: stats,
        meta: {
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Failed to get cost stats', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve cost statistics',
        message: (error as Error).message
      });
    }
  }

  /**
   * Update AI budget settings
   */
  async updateBudget(req: Request, res: Response): Promise<void> {
    try {
      const validatedData = budgetUpdateSchema.parse(req.body);
      
      aiEnhancedScraperService.setAIBudget(validatedData);
      
      logger.info('AI budget updated', validatedData);
      
      res.status(200).json({
        success: true,
        message: 'Budget updated successfully',
        data: validatedData
      });

    } catch (error) {
      logger.error('Failed to update budget', error);

      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.errors
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: 'Failed to update budget',
        message: (error as Error).message
      });
    }
  }

  /**
   * Health check endpoint
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const stats = aiEnhancedScraperService.getCostStats();
      
      res.status(200).json({
        success: true,
        status: 'healthy',
        data: {
          aiEnabled: true,
          costStats: stats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      res.status(503).json({
        success: false,
        status: 'unhealthy',
        error: (error as Error).message
      });
    }
  }
}

// Export singleton instance
export const aiEnhancedScraperController = new AIEnhancedScraperController();
export default aiEnhancedScraperController;
