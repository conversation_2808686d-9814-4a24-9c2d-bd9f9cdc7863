/**
 * AI-Enhanced Scraper Routes
 * Express routes for AI-powered web scraping endpoints
 */

import { Router } from 'express';
import { aiEnhancedScraperController } from './ai-enhanced-scraper.controller';
import logger from '../utils/logger';

const router = Router();

/**
 * Middleware for request logging
 */
router.use((req, res, next) => {
  logger.info(`AI Scraper API: ${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  next();
});

/**
 * Middleware for response time tracking
 */
router.use((req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    logger.info(`AI Scraper API Response: ${req.method} ${req.path}`, {
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length')
    });
  });
  
  next();
});

/**
 * @swagger
 * /api/ai-scraper/scrape:
 *   post:
 *     summary: Scrape a single URL with AI enhancement
 *     description: Extracts business intelligence, enhanced contact information, and sentiment analysis from a website
 *     tags:
 *       - AI Scraper
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - url
 *             properties:
 *               url:
 *                 type: string
 *                 format: uri
 *                 description: The URL to scrape
 *                 example: "https://example.com"
 *               options:
 *                 type: object
 *                 properties:
 *                   followRedirects:
 *                     type: boolean
 *                     default: true
 *                     description: Whether to follow redirects
 *                   maxDepth:
 *                     type: integer
 *                     minimum: 1
 *                     maximum: 5
 *                     default: 1
 *                     description: Maximum depth for link following
 *                   timeout:
 *                     type: integer
 *                     minimum: 1000
 *                     maximum: 60000
 *                     default: 30000
 *                     description: Request timeout in milliseconds
 *                   enableAI:
 *                     type: boolean
 *                     default: true
 *                     description: Whether to enable AI processing
 *                   aiOptions:
 *                     type: object
 *                     properties:
 *                       maxCost:
 *                         type: number
 *                         minimum: 0
 *                         maximum: 1
 *                         default: 0.10
 *                         description: Maximum cost per request in USD
 *                       useCache:
 *                         type: boolean
 *                         default: true
 *                         description: Whether to use AI response caching
 *                       cacheExpiryMinutes:
 *                         type: integer
 *                         minimum: 1
 *                         maximum: 1440
 *                         default: 60
 *                         description: Cache expiry time in minutes
 *     responses:
 *       200:
 *         description: Successful scraping with AI analysis
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                     title:
 *                       type: string
 *                     description:
 *                       type: string
 *                     businessIntelligence:
 *                       type: object
 *                     enhancedContactInfo:
 *                       type: object
 *                     extractedEntities:
 *                       type: object
 *                     sentiment:
 *                       type: object
 *                     aiCost:
 *                       type: number
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Internal server error
 */
router.post('/scrape', aiEnhancedScraperController.scrapeWithAI.bind(aiEnhancedScraperController));

/**
 * @swagger
 * /api/ai-scraper/batch-scrape:
 *   post:
 *     summary: Scrape multiple URLs with AI enhancement
 *     description: Batch process multiple URLs with AI analysis and cost control
 *     tags:
 *       - AI Scraper
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - urls
 *             properties:
 *               urls:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 minItems: 1
 *                 maxItems: 10
 *                 description: Array of URLs to scrape (max 10)
 *                 example: ["https://example1.com", "https://example2.com"]
 *               options:
 *                 type: object
 *                 properties:
 *                   followRedirects:
 *                     type: boolean
 *                     default: true
 *                   maxDepth:
 *                     type: integer
 *                     minimum: 1
 *                     maximum: 5
 *                     default: 1
 *                   timeout:
 *                     type: integer
 *                     minimum: 1000
 *                     maximum: 60000
 *                     default: 30000
 *                   enableAI:
 *                     type: boolean
 *                     default: true
 *                   aiOptions:
 *                     type: object
 *                     properties:
 *                       maxCostPerUrl:
 *                         type: number
 *                         minimum: 0
 *                         maximum: 1
 *                         default: 0.10
 *                         description: Maximum cost per URL in USD
 *                       maxTotalCost:
 *                         type: number
 *                         minimum: 0
 *                         maximum: 5
 *                         default: 1.00
 *                         description: Maximum total cost for batch in USD
 *                       useCache:
 *                         type: boolean
 *                         default: true
 *                       cacheExpiryMinutes:
 *                         type: integer
 *                         minimum: 1
 *                         maximum: 1440
 *                         default: 60
 *     responses:
 *       200:
 *         description: Successful batch scraping
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Internal server error
 */
router.post('/batch-scrape', aiEnhancedScraperController.batchScrapeWithAI.bind(aiEnhancedScraperController));

/**
 * @swagger
 * /api/ai-scraper/cost-stats:
 *   get:
 *     summary: Get AI cost statistics
 *     description: Retrieve current AI usage and cost statistics
 *     tags:
 *       - AI Scraper
 *     responses:
 *       200:
 *         description: Cost statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     today:
 *                       type: object
 *                       properties:
 *                         requests:
 *                           type: integer
 *                         cost:
 *                           type: number
 *                         tokens:
 *                           type: integer
 *                     thisMonth:
 *                       type: object
 *                     total:
 *                       type: object
 *                     averages:
 *                       type: object
 *       500:
 *         description: Internal server error
 */
router.get('/cost-stats', aiEnhancedScraperController.getCostStats.bind(aiEnhancedScraperController));

/**
 * @swagger
 * /api/ai-scraper/budget:
 *   put:
 *     summary: Update AI budget settings
 *     description: Configure daily, monthly, and per-request budget limits
 *     tags:
 *       - AI Scraper
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               daily:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *                 description: Daily budget limit in USD
 *               monthly:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 1000
 *                 description: Monthly budget limit in USD
 *               perRequest:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 1
 *                 description: Per-request budget limit in USD
 *     responses:
 *       200:
 *         description: Budget updated successfully
 *       400:
 *         description: Invalid budget parameters
 *       500:
 *         description: Internal server error
 */
router.put('/budget', aiEnhancedScraperController.updateBudget.bind(aiEnhancedScraperController));

/**
 * @swagger
 * /api/ai-scraper/health:
 *   get:
 *     summary: Health check for AI scraper service
 *     description: Check the health status of the AI-enhanced scraper service
 *     tags:
 *       - AI Scraper
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 status:
 *                   type: string
 *                   enum: [healthy]
 *                 data:
 *                   type: object
 *                   properties:
 *                     aiEnabled:
 *                       type: boolean
 *                     costStats:
 *                       type: object
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *       503:
 *         description: Service is unhealthy
 */
router.get('/health', aiEnhancedScraperController.healthCheck.bind(aiEnhancedScraperController));

export default router;
