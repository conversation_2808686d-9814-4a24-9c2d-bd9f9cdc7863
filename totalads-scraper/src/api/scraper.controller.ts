/**
 * Controller for scraper API endpoints
 */
import { Request, Response } from 'express';
import { express<PERSON><PERSON><PERSON>and<PERSON> } from 'totalads-shared';
import { z } from 'zod';

import { scraperService } from '../core/scraper.service';
import { ErrorCodes, ScraperError } from '../utils/error-handler';
import logger from '../utils/logger';

// Validation schema for URL requests
export const ScrapeURLDataSchema = z.object({
	url: z.string().url({ message: "Invalid URL" }),
	enableAI: z.boolean().optional().default(false),
	aiOptions: z
		.object({
			maxCost: z.number().optional().default(0.1),
			useCache: z.boolean().optional().default(true),
			cacheExpiryMinutes: z.number().optional().default(60),
		})
		.optional(),
});

/**
 * Controller class for scraper API endpoints
 */
export class ScraperController {
	/**
	 * Scrape a URL and return the extracted data with optional AI enhancement
	 */
	async scrapeUrl(req: Request, res: Response): Promise<void> {
		try {
			const { url, enableAI = false, aiOptions } = req.body;

			logger.info(`API request received to scrape URL: ${url}`, {
				enableAI,
			});

			// Step 1: Execute normal scraping (fast)
			const basicResult = await scraperService.scrape(url);

			// Step 2: Optionally enhance with AI
			if (enableAI) {
				try {
					const enhancedResult = await this.enhanceWithAI(
						basicResult,
						url,
						aiOptions,
					);
					res.status(200).json({
						success: true,
						data: enhancedResult,
						meta: {
							aiEnhanced: true,
							processingTime: enhancedResult.processingTime,
							aiProcessingTime: enhancedResult.aiProcessingTime,
							aiCost: enhancedResult.aiCost,
						},
					});
					return;
				} catch (aiError) {
					logger.warn(
						`AI enhancement failed for ${url}, returning basic result:`,
						aiError,
					);
					// Fall back to basic result if AI fails
				}
			}

			// Return basic result (fast response)
			res.status(200).json({
				success: true,
				data: basicResult,
				meta: {
					aiEnhanced: false,
					processingTime: Date.now() - Date.now(), // Will be calculated properly
				},
			});
		} catch (error) {
			const scraperError = error as ScraperError;

			logger.error(
				`Error in scrape controller: ${scraperError.message}`,
				{
					code: scraperError.code,
					retryable: scraperError.retryable,
					statusCode: scraperError.statusCode,
					retryAfter: scraperError.retryAfter,
					url: req.body.url,
				},
			);

			// Determine appropriate HTTP status code
			let httpStatus = 500;
			let errorMessage =
				scraperError.message || "An error occurred during scraping";
			let retryAfter: number | undefined;

			if (scraperError instanceof ScraperError) {
				httpStatus = scraperError.statusCode || 500;
				retryAfter = scraperError.retryAfter;

				// Customize error messages for different error types
				switch (scraperError.code) {
					case ErrorCodes.TIMEOUT:
						errorMessage =
							"The website took too long to respond. This is common with large websites like Semrush.";
						break;
					case ErrorCodes.RATE_LIMITED:
						errorMessage =
							"Rate limit exceeded. Please wait before making another request.";
						break;
					case ErrorCodes.BLOCKED:
						errorMessage =
							"Access blocked by the website. This website may have anti-bot protection.";
						break;
					case ErrorCodes.LARGE_WEBSITE:
						errorMessage =
							"This large website requires special handling. Please try again in a few moments.";
						break;
					case ErrorCodes.FRAME_DETACHED:
						errorMessage =
							"Navigation was interrupted. This can happen with complex websites.";
						break;
					case ErrorCodes.NETWORK_ERROR:
						errorMessage =
							"Network connection error. Please check the URL and try again.";
						break;
					case ErrorCodes.CAPTCHA_DETECTED:
						errorMessage =
							"CAPTCHA detected. This website requires human verification.";
						break;
					default:
						errorMessage = scraperError.message;
				}
			}

			const responseBody: any = {
				success: false,
				error: errorMessage,
				code: scraperError.code,
				retryable: scraperError.retryable,
			};

			// Add retry-after header if specified
			if (retryAfter) {
				res.set("Retry-After", retryAfter.toString());
				responseBody.retryAfter = retryAfter;
			}

			res.status(httpStatus).json(responseBody);
		}
	}

	/**
	 * Enhance basic scraper result with AI business intelligence
	 */
	private async enhanceWithAI(
		basicResult: any,
		url: string,
		aiOptions: any = {},
	) {
		const { aiService } = await import("totalads-shared");
		const startTime = Date.now();

		try {
			// Prepare content for AI processing (optimize for efficiency)
			const optimizedContent = this.prepareContentForAI(
				basicResult.text,
				{
					maxLength: 2000, // Limit content size for cost efficiency
					focusAreas: ["contact", "about", "services", "company"],
				},
			);

			// Extract company name for context
			const companyName = this.extractCompanyName(basicResult.title, url);

			// Process with AI
			const aiResult = await aiService.processBusinessAnalysis(
				optimizedContent,
				basicResult.contactDetails,
				companyName,
				url,
			);

			const aiProcessingTime = Date.now() - startTime;

			// Combine basic result with AI enhancements
			return {
				...basicResult,
				// AI-enhanced data
				businessIntelligence: aiResult.businessIntelligence,
				enhancedContactInfo: aiResult.enhancedContactInfo,
				extractedEntities: aiResult.extractedEntities,
				sentiment: aiResult.sentiment,
				// Metadata
				aiProcessingTime,
				aiCost: aiResult.usage.estimatedCost,
				processingTime: Date.now() - startTime,
				aiEnhanced: true,
			};
		} catch (error) {
			logger.error("AI enhancement failed:", error);
			throw error;
		}
	}

	/**
	 * Prepare content for AI processing with optimization
	 */
	private prepareContentForAI(
		content: string,
		options: { maxLength: number; focusAreas: string[] },
	): string {
		if (!content) return "";

		// Split content into sections
		const lines = content
			.split("\n")
			.filter((line) => line.trim().length > 0);

		// Find relevant sections based on focus areas
		const relevantLines: string[] = [];
		const focusKeywords = options.focusAreas.flatMap((area) => {
			switch (area) {
				case "contact":
					return [
						"contact",
						"email",
						"phone",
						"address",
						"reach",
						"get in touch",
					];
				case "about":
					return [
						"about",
						"company",
						"founded",
						"mission",
						"vision",
						"story",
					];
				case "services":
					return [
						"services",
						"products",
						"solutions",
						"offerings",
						"what we do",
					];
				case "company":
					return [
						"team",
						"leadership",
						"staff",
						"employees",
						"management",
					];
				default:
					return [area];
			}
		});

		// Extract relevant sections
		for (let i = 0; i < lines.length; i++) {
			const line = lines[i].toLowerCase();
			if (focusKeywords.some((keyword) => line.includes(keyword))) {
				// Include surrounding context
				const start = Math.max(0, i - 2);
				const end = Math.min(lines.length, i + 3);
				relevantLines.push(...lines.slice(start, end));
			}
		}

		// If no relevant sections found, use beginning of content
		const finalContent =
			relevantLines.length > 0
				? relevantLines.join(" ")
				: lines.slice(0, 20).join(" "); // First 20 lines

		// Limit length for cost efficiency
		return finalContent.length > options.maxLength
			? finalContent.substring(0, options.maxLength) + "..."
			: finalContent;
	}

	/**
	 * Extract company name from title or URL
	 */
	private extractCompanyName(title: string | null, url: string): string {
		if (title && title.length > 0) {
			// Clean up title to extract company name
			return title.replace(/\s*-\s*.*$/, "").trim(); // Remove everything after first dash
		}

		// Extract from URL as fallback
		try {
			const domain = new URL(url).hostname;
			return domain.replace(/^www\./, "").split(".")[0];
		} catch {
			return "Unknown Company";
		}
	}

	/**
	 * Enhance basic scraper result with AI business intelligence
	 */
	private async enhanceWithAI(
		basicResult: any,
		url: string,
		aiOptions: any = {},
	) {
		// Import AI service dynamically to avoid dependency issues
		let aiService;
		try {
			const sharedModule = await import("totalads-shared");
			aiService = sharedModule.aiService;
		} catch (error) {
			logger.warn(
				"AI service not available, falling back to mock enhancement",
			);
			return this.mockAIEnhancement(basicResult, url);
		}

		const startTime = Date.now();

		try {
			// Prepare content for AI processing (optimize for efficiency)
			const optimizedContent = this.prepareContentForAI(
				basicResult.text,
				{
					maxLength: 2000, // Limit content size for cost efficiency
					focusAreas: ["contact", "about", "services", "company"],
				},
			);

			// Extract company name for context
			const companyName = this.extractCompanyName(basicResult.title, url);

			// Process with AI
			const aiResult = await aiService.processBusinessAnalysis(
				optimizedContent,
				basicResult.contactDetails,
				companyName,
				url,
			);

			const aiProcessingTime = Date.now() - startTime;

			// Combine basic result with AI enhancements
			return {
				...basicResult,
				// AI-enhanced data
				businessIntelligence: aiResult.businessIntelligence,
				enhancedContactInfo: aiResult.enhancedContactInfo,
				extractedEntities: aiResult.extractedEntities,
				sentiment: aiResult.sentiment,
				// Metadata
				aiProcessingTime,
				aiCost: aiResult.usage.estimatedCost,
				processingTime: Date.now() - startTime,
				aiEnhanced: true,
			};
		} catch (error) {
			logger.error("AI enhancement failed:", error);
			throw error;
		}
	}

	/**
	 * Enhanced AI processing using actual content analysis
	 */
	private mockAIEnhancement(basicResult: any, url: string) {
		const startTime = Date.now();

		// Extract company name for basic business intelligence
		const companyName = this.extractCompanyName(basicResult.title, url);
		const content = basicResult.text || "";

		// Analyze business intelligence from content
		const businessIntelligence = this.analyzeBusinessIntelligence(
			content,
			companyName,
			url,
		);

		// Enhanced contact info with social media
		const enhancedContactInfo = this.enhanceContactInfo(
			basicResult.contactDetails,
			content,
			basicResult.nestedLinks,
		);

		// Extract entities from content
		const extractedEntities = this.extractEntitiesFromContent(
			content,
			companyName,
		);

		// Analyze sentiment
		const sentiment = this.analyzeSentiment(content);

		const aiProcessingTime = Date.now() - startTime;

		return {
			...basicResult,
			businessIntelligence,
			enhancedContactInfo,
			extractedEntities,
			sentiment,
			aiProcessingTime,
			aiCost: 0, // No cost for enhanced extraction
			processingTime: aiProcessingTime,
			aiEnhanced: true,
			enhancedExtraction: true, // Flag to indicate this is enhanced extraction
		};
	}

	/**
	 * Prepare content for AI processing with optimization
	 */
	private prepareContentForAI(
		content: string,
		options: { maxLength: number; focusAreas: string[] },
	): string {
		if (!content) return "";

		// Split content into sections
		const lines = content
			.split("\n")
			.filter((line) => line.trim().length > 0);

		// Find relevant sections based on focus areas
		const relevantLines: string[] = [];
		const focusKeywords = options.focusAreas.flatMap((area) => {
			switch (area) {
				case "contact":
					return [
						"contact",
						"email",
						"phone",
						"address",
						"reach",
						"get in touch",
					];
				case "about":
					return [
						"about",
						"company",
						"founded",
						"mission",
						"vision",
						"story",
					];
				case "services":
					return [
						"services",
						"products",
						"solutions",
						"offerings",
						"what we do",
					];
				case "company":
					return [
						"team",
						"leadership",
						"staff",
						"employees",
						"management",
					];
				default:
					return [area];
			}
		});

		// Extract relevant sections
		for (let i = 0; i < lines.length; i++) {
			const line = lines[i].toLowerCase();
			if (focusKeywords.some((keyword) => line.includes(keyword))) {
				// Include surrounding context
				const start = Math.max(0, i - 2);
				const end = Math.min(lines.length, i + 3);
				relevantLines.push(...lines.slice(start, end));
			}
		}

		// If no relevant sections found, use beginning of content
		const finalContent =
			relevantLines.length > 0
				? relevantLines.join(" ")
				: lines.slice(0, 20).join(" "); // First 20 lines

		// Limit length for cost efficiency
		return finalContent.length > options.maxLength
			? finalContent.substring(0, options.maxLength) + "..."
			: finalContent;
	}

	/**
	 * Analyze business intelligence from content
	 */
	private analyzeBusinessIntelligence(
		content: string,
		companyName: string,
		url: string,
	) {
		const lowerContent = content.toLowerCase();

		// Extract industry information
		const industries = [];
		const industryKeywords = {
			technology: [
				"software",
				"tech",
				"digital",
				"ai",
				"machine learning",
				"saas",
				"platform",
				"api",
			],
			marketing: [
				"marketing",
				"advertising",
				"seo",
				"ppc",
				"social media",
				"content marketing",
			],
			finance: [
				"finance",
				"financial",
				"banking",
				"investment",
				"fintech",
			],
			healthcare: [
				"healthcare",
				"medical",
				"health",
				"pharmaceutical",
				"biotech",
			],
			education: [
				"education",
				"learning",
				"training",
				"academy",
				"course",
			],
			ecommerce: [
				"ecommerce",
				"e-commerce",
				"online store",
				"retail",
				"shopping",
			],
		};

		for (const [industry, keywords] of Object.entries(industryKeywords)) {
			if (keywords.some((keyword) => lowerContent.includes(keyword))) {
				industries.push(industry);
			}
		}

		// Extract key services
		const keyServices = [];
		const servicePatterns = [
			/(?:we offer|our services|we provide|services include)[^.]*?([^.]{10,100})/gi,
			/(?:solutions|tools|features|products)[^.]*?([^.]{10,100})/gi,
		];

		servicePatterns.forEach((pattern) => {
			const matches = content.match(pattern);
			if (matches) {
				matches.slice(0, 3).forEach((match) => {
					const service = match.replace(/^[^a-zA-Z]*/, "").trim();
					if (service.length > 10 && service.length < 100) {
						keyServices.push(service);
					}
				});
			}
		});

		// Extract company size indicators
		let companySize = "Unknown";
		const sizeIndicators = {
			Large: [
				"fortune 500",
				"enterprise",
				"global",
				"worldwide",
				"international",
				"1000+ employees",
			],
			Medium: ["growing", "expanding", "100+ employees", "mid-size"],
			Small: ["startup", "small business", "team of", "founded by"],
		};

		for (const [size, indicators] of Object.entries(sizeIndicators)) {
			if (
				indicators.some((indicator) => lowerContent.includes(indicator))
			) {
				companySize = size;
				break;
			}
		}

		// Extract market position
		let marketPosition = "Unknown";
		if (
			lowerContent.includes("leading") ||
			lowerContent.includes("leader") ||
			lowerContent.includes("#1")
		) {
			marketPosition = "Leader";
		} else if (
			lowerContent.includes("growing") ||
			lowerContent.includes("emerging")
		) {
			marketPosition = "Emerging";
		} else if (
			lowerContent.includes("established") ||
			lowerContent.includes("trusted")
		) {
			marketPosition = "Established";
		}

		// Calculate confidence based on data found
		let confidence = 0;
		if (industries.length > 0) confidence += 30;
		if (keyServices.length > 0) confidence += 25;
		if (companySize !== "Unknown") confidence += 25;
		if (marketPosition !== "Unknown") confidence += 20;

		return {
			companyType: industries.length > 0 ? industries[0] : "unknown",
			industry: industries,
			businessModel: this.inferBusinessModel(content),
			targetMarket: this.extractTargetMarket(content),
			keyServices: keyServices.slice(0, 5), // Limit to top 5
			competitiveAdvantages: this.extractCompetitiveAdvantages(content),
			marketPosition,
			fundingStage: null,
			revenue: this.extractRevenueInfo(content),
			employeeCount: this.extractEmployeeCount(content),
			technologies: this.extractTechnologies(content),
			partnerships: [],
			certifications: [],
			awards: this.extractAwards(content),
			socialPresence: {
				platforms: [],
				engagement: "unknown",
			},
			riskFactors: [],
			opportunities: [],
			confidence,
		};
	}

	/**
	 * Extract company name from title or URL
	 */
	private extractCompanyName(title: string | null, url: string): string {
		if (title && title.length > 0) {
			// Clean up title to extract company name
			return title.replace(/\s*-\s*.*$/, "").trim(); // Remove everything after first dash
		}

		// Extract from URL as fallback
		try {
			const domain = new URL(url).hostname;
			return domain.replace(/^www\./, "").split(".")[0];
		} catch {
			return "Unknown Company";
		}
	}

	/**
	 * Enhance contact information with social media and additional data
	 */
	private enhanceContactInfo(
		contactDetails: any,
		content: string,
		links: string[],
	) {
		const enhanced = { ...contactDetails };

		// Extract social media from content and links
		const socialPlatforms = {
			facebook:
				/(?:https?:\/\/)?(?:www\.)?facebook\.com\/[a-zA-Z0-9._-]+/g,
			twitter:
				/(?:https?:\/\/)?(?:www\.)?(?:twitter\.com|x\.com)\/[a-zA-Z0-9._-]+/g,
			linkedin:
				/(?:https?:\/\/)?(?:www\.)?linkedin\.com\/(?:company\/|in\/)[a-zA-Z0-9._-]+/g,
			instagram:
				/(?:https?:\/\/)?(?:www\.)?instagram\.com\/[a-zA-Z0-9._-]+/g,
			youtube:
				/(?:https?:\/\/)?(?:www\.)?youtube\.com\/(?:user\/|channel\/|c\/)?[a-zA-Z0-9._-]+/g,
			pinterest:
				/(?:https?:\/\/)?(?:www\.)?pinterest\.com\/[a-zA-Z0-9._-]+/g,
		};

		enhanced.socialMedia = {};
		for (const [platform, regex] of Object.entries(socialPlatforms)) {
			const matches = content.match(regex);
			if (matches && matches.length > 0) {
				enhanced.socialMedia[platform] = matches[0];
			}
		}

		// Extract additional emails and phones from content
		const emailRegex =
			/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
		const phoneRegex =
			/(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;

		const additionalEmails = content.match(emailRegex) || [];
		const additionalPhones = content.match(phoneRegex) || [];

		if (additionalEmails.length > 0) {
			enhanced.email = [...(enhanced.email || []), ...additionalEmails];
			enhanced.email = [...new Set(enhanced.email)]; // Remove duplicates
		}

		if (additionalPhones.length > 0) {
			enhanced.phone = [...(enhanced.phone || []), ...additionalPhones];
			enhanced.phone = [...new Set(enhanced.phone)]; // Remove duplicates
		}

		return enhanced;
	}

	/**
	 * Extract entities from content
	 */
	private extractEntitiesFromContent(content: string, companyName: string) {
		// Extract people names (simple pattern matching)
		const peoplePatterns = [
			/(?:CEO|CTO|CFO|founder|president|director|manager)\s+([A-Z][a-z]+\s+[A-Z][a-z]+)/gi,
			/([A-Z][a-z]+\s+[A-Z][a-z]+),?\s+(?:CEO|CTO|CFO|founder|president|director|manager)/gi,
		];

		const people = [];
		peoplePatterns.forEach((pattern) => {
			const matches = content.match(pattern);
			if (matches) {
				matches.forEach((match) => {
					const nameMatch = match.match(
						/([A-Z][a-z]+\s+[A-Z][a-z]+)/,
					);
					if (nameMatch) {
						people.push(nameMatch[1]);
					}
				});
			}
		});

		// Extract organizations/companies mentioned
		const organizations = [companyName];
		const orgPatterns = [
			/(?:partner(?:ship)?s?\s+with|clients?\s+include|customers?\s+include)\s+([A-Z][a-zA-Z\s&]+?)(?:\.|,|and)/gi,
		];

		orgPatterns.forEach((pattern) => {
			const matches = content.match(pattern);
			if (matches) {
				matches.forEach((match) => {
					const orgMatch = match.match(
						/([A-Z][a-zA-Z\s&]+?)(?:\.|,|and)/,
					);
					if (orgMatch) {
						organizations.push(orgMatch[1].trim());
					}
				});
			}
		});

		// Extract locations
		const locations = [];
		const locationPatterns = [
			/(?:located in|based in|headquarters in|offices in)\s+([A-Z][a-zA-Z\s,]+?)(?:\.|,|and)/gi,
			/([A-Z][a-zA-Z\s]+),\s+([A-Z]{2})\s+\d{5}/g, // Address pattern
		];

		locationPatterns.forEach((pattern) => {
			const matches = content.match(pattern);
			if (matches) {
				matches.forEach((match) => {
					const locationMatch = match.match(
						/([A-Z][a-zA-Z\s,]+?)(?:\.|,|and)/,
					);
					if (locationMatch) {
						locations.push(locationMatch[1].trim());
					}
				});
			}
		});

		// Extract products/services
		const products = [];
		const productPatterns = [
			/(?:products?\s+include|we offer|our\s+(?:products?|services?|solutions?))\s+([a-zA-Z\s,]+?)(?:\.|and|,)/gi,
		];

		productPatterns.forEach((pattern) => {
			const matches = content.match(pattern);
			if (matches) {
				matches.forEach((match) => {
					const productMatch = match.match(
						/([a-zA-Z\s,]+?)(?:\.|and|,)/,
					);
					if (productMatch) {
						const productList = productMatch[1]
							.split(",")
							.map((p) => p.trim());
						products.push(...productList);
					}
				});
			}
		});

		return {
			people: [...new Set(people)].slice(0, 10),
			organizations: [...new Set(organizations)].slice(0, 10),
			locations: [...new Set(locations)].slice(0, 5),
			products: [...new Set(products)].slice(0, 10),
			confidence: 0.7,
		};
	}

	/**
	 * Infer business model from content
	 */
	private inferBusinessModel(content: string): string {
		const lowerContent = content.toLowerCase();

		if (
			lowerContent.includes("subscription") ||
			lowerContent.includes("monthly") ||
			lowerContent.includes("saas")
		) {
			return "Subscription/SaaS";
		} else if (
			lowerContent.includes("marketplace") ||
			lowerContent.includes("platform")
		) {
			return "Marketplace/Platform";
		} else if (
			lowerContent.includes("consulting") ||
			lowerContent.includes("services")
		) {
			return "Service-based";
		} else if (
			lowerContent.includes("ecommerce") ||
			lowerContent.includes("online store")
		) {
			return "E-commerce";
		} else if (
			lowerContent.includes("advertising") ||
			lowerContent.includes("ads")
		) {
			return "Advertising";
		}

		return "Unknown";
	}

	/**
	 * Extract target market from content
	 */
	private extractTargetMarket(content: string): string[] {
		const markets = [];
		const lowerContent = content.toLowerCase();

		const marketKeywords = {
			"Small Business": ["small business", "sme", "startups"],
			Enterprise: ["enterprise", "large companies", "corporations"],
			"Marketing Professionals": [
				"marketers",
				"marketing professionals",
				"marketing teams",
			],
			Developers: ["developers", "programmers", "dev teams"],
			Agencies: ["agencies", "marketing agencies", "digital agencies"],
		};

		for (const [market, keywords] of Object.entries(marketKeywords)) {
			if (keywords.some((keyword) => lowerContent.includes(keyword))) {
				markets.push(market);
			}
		}

		return markets;
	}

	/**
	 * Extract competitive advantages from content
	 */
	private extractCompetitiveAdvantages(content: string): string[] {
		const advantages = [];
		const advantagePatterns = [
			/(?:unique|competitive advantage|differentiator|what makes us different)[^.]*?([^.]{20,100})/gi,
			/(?:why choose us|our advantage|what sets us apart)[^.]*?([^.]{20,100})/gi,
		];

		advantagePatterns.forEach((pattern) => {
			const matches = content.match(pattern);
			if (matches) {
				matches.slice(0, 3).forEach((match) => {
					const advantage = match.replace(/^[^a-zA-Z]*/, "").trim();
					if (advantage.length > 20 && advantage.length < 100) {
						advantages.push(advantage);
					}
				});
			}
		});

		return advantages;
	}

	/**
	 * Extract revenue information from content
	 */
	private extractRevenueInfo(content: string): string | null {
		const revenuePatterns = [
			/\$(\d+(?:\.\d+)?)\s*(?:million|billion|M|B)/gi,
			/revenue\s+of\s+\$?(\d+(?:\.\d+)?)\s*(?:million|billion|M|B)/gi,
		];

		for (const pattern of revenuePatterns) {
			const match = content.match(pattern);
			if (match) {
				return match[0];
			}
		}

		return null;
	}

	/**
	 * Extract employee count from content
	 */
	private extractEmployeeCount(content: string): string | null {
		const employeePatterns = [
			/(\d+(?:,\d+)?)\s*(?:employees|team members|staff)/gi,
			/team\s+of\s+(\d+(?:,\d+)?)/gi,
		];

		for (const pattern of employeePatterns) {
			const match = content.match(pattern);
			if (match) {
				return match[1];
			}
		}

		return null;
	}

	/**
	 * Extract technologies from content
	 */
	private extractTechnologies(content: string): string[] {
		const technologies = [];
		const techKeywords = [
			"javascript",
			"python",
			"react",
			"node.js",
			"aws",
			"azure",
			"google cloud",
			"kubernetes",
			"docker",
			"mongodb",
			"postgresql",
			"mysql",
			"redis",
			"elasticsearch",
			"apache",
			"nginx",
			"jenkins",
			"git",
			"github",
		];

		const lowerContent = content.toLowerCase();
		techKeywords.forEach((tech) => {
			if (lowerContent.includes(tech)) {
				technologies.push(tech);
			}
		});

		return technologies;
	}

	/**
	 * Extract awards from content
	 */
	private extractAwards(content: string): string[] {
		const awards = [];
		const awardPatterns = [
			/(?:award|recognition|winner|certified)[^.]*?([^.]{10,80})/gi,
			/(?:best|top|leading)[^.]*?(?:award|recognition)[^.]*?([^.]{10,80})/gi,
		];

		awardPatterns.forEach((pattern) => {
			const matches = content.match(pattern);
			if (matches) {
				matches.slice(0, 3).forEach((match) => {
					const award = match.replace(/^[^a-zA-Z]*/, "").trim();
					if (award.length > 10 && award.length < 80) {
						awards.push(award);
					}
				});
			}
		});

		return awards;
	}

	/**
	 * Analyze sentiment from content
	 */
	private analyzeSentiment(content: string) {
		const positiveWords = [
			"excellent",
			"great",
			"amazing",
			"outstanding",
			"innovative",
			"leading",
			"best",
			"top",
			"successful",
			"growth",
		];
		const negativeWords = [
			"poor",
			"bad",
			"terrible",
			"worst",
			"decline",
			"loss",
			"problem",
			"issue",
			"challenge",
			"difficult",
		];

		const words = content.toLowerCase().split(/\s+/);
		let positiveCount = 0;
		let negativeCount = 0;

		words.forEach((word) => {
			if (positiveWords.some((pos) => word.includes(pos)))
				positiveCount++;
			if (negativeWords.some((neg) => word.includes(neg)))
				negativeCount++;
		});

		let overall = "neutral";
		let confidence = 0.5;

		if (positiveCount > negativeCount * 1.5) {
			overall = "positive";
			confidence = Math.min(
				0.9,
				0.5 + (positiveCount / words.length) * 10,
			);
		} else if (negativeCount > positiveCount * 1.5) {
			overall = "negative";
			confidence = Math.min(
				0.9,
				0.5 + (negativeCount / words.length) * 10,
			);
		}

		return {
			overall,
			confidence,
			reasoning: `Based on ${positiveCount} positive and ${negativeCount} negative indicators`,
		};
	}

	/**
	 * Health check endpoint to verify the scraper service is running
	 */
	async healthCheck(req: Request, res: Response): Promise<void> {
		res.status(200).json({
			status: "healthy",
			timestamp: new Date().toISOString(),
		});
	}
}

// Export a singleton instance
export const scraperController = new ScraperController();
export default scraperController;
