/**
 * AI Service for Business Intelligence Extraction
 * Optimized for scraping enhancement with minimal cost
 */

import { aiClient, TokenUsage } from '../config/ai';

export interface BusinessIntelligence {
	companyType: string;
	industry: string[];
	businessModel: string;
	targetMarket: string[];
	keyServices: string[];
	competitiveAdvantages: string[];
	marketPosition:
		| "startup"
		| "small"
		| "medium"
		| "large"
		| "enterprise"
		| "unknown";
	fundingStage?: string;
	revenue?: string;
	employeeCount?: string;
	technologies: string[];
	partnerships: string[];
	certifications: string[];
	awards: string[];
	socialPresence: {
		platforms: string[];
		engagement: "low" | "medium" | "high" | "unknown";
	};
	riskFactors: string[];
	opportunities: string[];
	confidence: number; // 0-100
}

export interface EnhancedContactInfo {
	emails: {
		address: string;
		type: "general" | "sales" | "support" | "hr" | "media" | "unknown";
		confidence: number;
	}[];
	phones: {
		number: string;
		type: "main" | "sales" | "support" | "mobile" | "fax" | "unknown";
		confidence: number;
	}[];
	addresses: {
		address: string;
		type: "headquarters" | "office" | "warehouse" | "store" | "unknown";
		confidence: number;
	}[];
	socialMedia: {
		platform: string;
		url: string;
		verified: boolean;
	}[];
	website: {
		primary: string;
		additional: string[];
	};
}

export interface AIProcessingResult {
	businessIntelligence: BusinessIntelligence;
	enhancedContactInfo: EnhancedContactInfo;
	extractedEntities: {
		people: string[];
		organizations: string[];
		locations: string[];
		products: string[];
	};
	sentiment: {
		overall: "positive" | "neutral" | "negative";
		confidence: number;
	};
	usage: TokenUsage;
	processingTime: number;
}

/**
 * AI Service for enhanced business data extraction
 */
export class AIService {
	private static instance: AIService;

	private constructor() {}

	public static getInstance(): AIService {
		if (!AIService.instance) {
			AIService.instance = new AIService();
		}
		return AIService.instance;
	}

	/**
	 * Extract business intelligence from scraped content
	 */
	async extractBusinessIntelligence(
		content: string,
		companyName?: string,
		websiteUrl?: string
	): Promise<BusinessIntelligence> {
		const startTime = Date.now();

		// Optimize content length but keep more content for better analysis
		const optimizedContent = this.optimizeContentForAI(content, 4000);

		const prompt = `
You are a professional business intelligence analyst. Extract concise, actionable business insights from the company content. Focus on quality over quantity - provide only valuable information that would be useful for business decision-making.

Company: ${companyName || "Unknown"}
Website: ${websiteUrl || "Unknown"}

ANALYSIS GUIDELINES:
1. Extract only factual, verifiable business information
2. Keep descriptions concise and professional (1-2 sentences max)
3. Focus on unique value propositions and competitive differentiators
4. Avoid generic or obvious information
5. Make intelligent inferences only when strongly supported by evidence
6. Use "Not specified" for unclear or missing information
7. Prioritize actionable business intelligence over comprehensive data collection
8. Ensure all extracted data provides real business value

Return ONLY valid JSON (no markdown formatting):
{
  "companyType": "corporation|llc|partnership|startup|enterprise|saas|unknown",
  "industry": ["primary industry only", "key secondary industry if relevant"],
  "businessModel": "B2B|B2C|B2B2C|marketplace|saas|freemium|subscription|unknown",
  "targetMarket": ["specific target segments", "key customer types"],
  "keyServices": ["core services/products only", "main offerings"],
  "competitiveAdvantages": ["unique selling points", "key differentiators"],
  "marketPosition": "startup|small|medium|large|enterprise",
  "fundingStage": "bootstrap|seed|series-a|series-b|series-c|ipo|acquired|unknown",
  "revenue": "estimate if clearly indicated or 'Not specified'",
  "employeeCount": "estimate if clearly indicated or 'Not specified'",
  "technologies": ["key technologies mentioned", "main platforms"],
  "partnerships": ["notable partners mentioned", "key integrations"],
  "certifications": ["relevant certifications mentioned"],
  "awards": ["significant awards or recognition"],
  "socialPresence": {
    "platforms": ["social platforms found"],
    "engagement": "low|medium|high|unknown"
  },
  "riskFactors": ["key business risks identified"],
  "opportunities": ["growth opportunities identified"],
  "confidence": number (0-100)
}

CONTENT TO ANALYZE:
${optimizedContent}
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: true,
					cacheExpiryMinutes: 120, // Cache for 2 hours
				}
			);

			// Extract JSON from markdown code blocks if present
			const cleanedContent = this.extractJsonFromResponse(result.content);
			const businessIntelligence = JSON.parse(
				cleanedContent
			) as BusinessIntelligence;

			// Validate and sanitize the result
			return this.validateBusinessIntelligence(businessIntelligence);
		} catch (error) {
			console.error("Error extracting business intelligence:", error);
			return this.getDefaultBusinessIntelligence();
		}
	}

	/**
	 * Enhance contact information using AI
	 */
	async enhanceContactInfo(
		rawContactData: any,
		content: string
	): Promise<EnhancedContactInfo> {
		const optimizedContent = this.optimizeContentForAI(content, 3000);

		const prompt = `
You are a professional contact information analyst. Extract only clear, verifiable contact information from the content. Focus on quality and accuracy over quantity.

Raw contact data: ${JSON.stringify(rawContactData)}

EXTRACTION GUIDELINES:
1. Extract only clearly visible email addresses and phone numbers
2. Include only legitimate business addresses (not generic locations)
3. Extract social media links that are clearly associated with the company
4. Focus on primary contact methods, not every possible variation
5. Use "Not found" for missing information instead of guessing
6. Ensure all extracted data is actionable and professional

Return ONLY valid JSON (no markdown formatting):
{
  "emails": [{"address": "<EMAIL>", "type": "general|sales|support|hr|media|info|contact", "confidence": number}],
  "phones": [{"number": "formatted number", "type": "main|sales|support|toll-free", "confidence": number}],
  "addresses": [{"address": "full address", "type": "headquarters|office|mailing", "confidence": number}],
  "socialMedia": [{"platform": "facebook|twitter|linkedin|instagram|youtube|pinterest", "url": "full url", "verified": boolean}],
  "website": {"primary": "main website", "additional": ["key subdomains or related sites"]}
}

CONTENT TO ANALYZE:
${optimizedContent}
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: true,
					cacheExpiryMinutes: 60,
				}
			);

			// Extract JSON from markdown code blocks if present
			const cleanedContent = this.extractJsonFromResponse(result.content);
			return JSON.parse(cleanedContent) as EnhancedContactInfo;
		} catch (error) {
			console.error("Error enhancing contact info:", error);
			return this.getDefaultContactInfo();
		}
	}

	/**
	 * Extract entities and sentiment from content
	 */
	async extractEntitiesAndSentiment(content: string): Promise<{
		entities: {
			people: string[];
			organizations: string[];
			locations: string[];
			products: string[];
		};
		sentiment: {
			overall: "positive" | "neutral" | "negative";
			confidence: number;
		};
	}> {
		const optimizedContent = this.optimizeContentForAI(content, 2500);

		const prompt = `
You are a professional entity extraction and sentiment analysis specialist. Extract key business entities and analyze sentiment with focus on actionable insights.

EXTRACTION GUIDELINES:
1. Extract only clearly mentioned person names (executives, founders, key team members)
2. Identify notable organizations (key partners, major clients, important integrations)
3. Find primary locations (headquarters, main offices, key markets)
4. Extract core products and services (main offerings, key features)
5. Analyze sentiment based on professional tone, customer focus, and market positioning
6. Focus on entities that provide business value and context
7. Avoid extracting generic or obvious information

Return ONLY valid JSON (no markdown formatting):
{
  "entities": {
    "people": ["key executives", "founders", "notable team members"],
    "organizations": ["key partners", "major clients", "important integrations"],
    "locations": ["headquarters", "main offices", "key markets"],
    "products": ["core products", "main services", "key features"]
  },
  "sentiment": {
    "overall": "positive|neutral|negative",
    "confidence": number (0-100)
  }
}

CONTENT TO ANALYZE:
${optimizedContent}
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: true,
					cacheExpiryMinutes: 30,
				}
			);

			// Extract JSON from markdown code blocks if present
			const cleanedContent = this.extractJsonFromResponse(result.content);
			return JSON.parse(cleanedContent);
		} catch (error) {
			console.error("Error extracting entities and sentiment:", error);
			return {
				entities: {
					people: [],
					organizations: [],
					locations: [],
					products: [],
				},
				sentiment: { overall: "neutral", confidence: 0 },
			};
		}
	}

	/**
	 * Process complete business analysis
	 */
	async processBusinessAnalysis(
		content: string,
		rawContactData: any,
		companyName?: string,
		websiteUrl?: string
	): Promise<AIProcessingResult> {
		const startTime = Date.now();

		try {
			// Run all AI analyses in parallel for efficiency
			const [
				businessIntelligence,
				enhancedContactInfo,
				entitiesAndSentiment,
			] = await Promise.all([
				this.extractBusinessIntelligence(
					content,
					companyName,
					websiteUrl
				),
				this.enhanceContactInfo(rawContactData, content),
				this.extractEntitiesAndSentiment(content),
			]);

			const processingTime = Date.now() - startTime;

			return {
				businessIntelligence,
				enhancedContactInfo,
				extractedEntities: entitiesAndSentiment.entities,
				sentiment: entitiesAndSentiment.sentiment,
				usage: {
					promptTokens: 0, // Will be calculated by aiClient
					completionTokens: 0,
					totalTokens: 0,
					estimatedCost: 0,
				},
				processingTime,
			};
		} catch (error) {
			console.error("Error in business analysis:", error);
			throw error;
		}
	}

	/**
	 * Extract JSON from AI response, handling markdown code blocks
	 */
	private extractJsonFromResponse(content: string): string {
		// Remove markdown code blocks if present
		const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
		if (jsonMatch) {
			return jsonMatch[1];
		}

		// If no code blocks, try to find JSON object
		const jsonObjectMatch = content.match(/\{[\s\S]*\}/);
		if (jsonObjectMatch) {
			return jsonObjectMatch[0];
		}

		// Return original content if no JSON found
		return content.trim();
	}

	/**
	 * Optimize content length for AI processing to reduce costs
	 */
	private optimizeContentForAI(content: string, maxLength: number): string {
		if (content.length <= maxLength) {
			return content;
		}

		// Enhanced business keywords for better content selection
		const businessKeywords = [
			// Core business terms
			"company",
			"business",
			"service",
			"product",
			"industry",
			"market",
			"customer",
			"client",
			"solution",
			"technology",
			"innovation",
			"team",
			"about",
			"mission",
			"vision",
			"value",
			"experience",

			// Contact and location terms
			"contact",
			"email",
			"phone",
			"address",
			"office",
			"headquarters",
			"location",
			"call",
			"reach",
			"support",
			"sales",
			"info",

			// Social and partnership terms
			"partner",
			"integration",
			"social",
			"follow",
			"connect",
			"linkedin",
			"twitter",
			"facebook",
			"instagram",
			"youtube",
			"collaboration",

			// Business intelligence terms
			"award",
			"certification",
			"recognition",
			"achievement",
			"founded",
			"established",
			"revenue",
			"employee",
			"staff",
			"global",
			"international",
			"enterprise",
			"startup",
			"funding",
			"investment",
			"acquisition",

			// Technology and features
			"platform",
			"software",
			"tool",
			"feature",
			"api",
			"integration",
			"cloud",
			"saas",
			"analytics",
			"data",
			"ai",
			"machine learning",

			// Market and competitive terms
			"leader",
			"leading",
			"competitive",
			"advantage",
			"unique",
			"best",
			"top",
			"premier",
			"trusted",
			"proven",
			"expert",
			"specialist",
		];

		// Split content into sentences
		const sentences = content.split(/[.!?]+/);

		// Score sentences based on business relevance with enhanced scoring
		const scoredSentences = sentences.map((sentence) => {
			const lowerSentence = sentence.toLowerCase();
			let score = 0;

			// Base keyword scoring
			businessKeywords.forEach((keyword) => {
				if (lowerSentence.includes(keyword)) {
					score += 1;
				}
			});

			// Bonus scoring for specific patterns
			if (lowerSentence.includes("@") && lowerSentence.includes("."))
				score += 3; // Email patterns
			if (
				lowerSentence.match(
					/\+?\d{1,3}[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/
				)
			)
				score += 3; // Phone patterns
			if (
				lowerSentence.includes("http") ||
				lowerSentence.includes("www.")
			)
				score += 2; // URL patterns
			if (
				lowerSentence.includes("founded") ||
				lowerSentence.includes("established")
			)
				score += 2; // Company history
			if (
				lowerSentence.includes("million") ||
				lowerSentence.includes("billion")
			)
				score += 2; // Financial info
			if (
				lowerSentence.includes("employees") ||
				lowerSentence.includes("team of")
			)
				score += 2; // Size info

			// Length bonus for substantial sentences
			if (sentence.length > 50) score += 1;
			if (sentence.length > 100) score += 1;

			return {
				sentence: sentence.trim(),
				score,
				length: sentence.length,
			};
		});

		// Sort by score (descending) and take top sentences
		scoredSentences.sort((a, b) => b.score - a.score);

		let optimizedContent = "";
		let currentLength = 0;

		// First pass: Add high-scoring sentences
		for (const item of scoredSentences) {
			if (item.score > 0 && currentLength + item.length <= maxLength) {
				optimizedContent += item.sentence + ". ";
				currentLength += item.length + 2;
			}
		}

		// Second pass: Fill remaining space with any remaining content
		if (currentLength < maxLength * 0.8) {
			for (const item of scoredSentences) {
				if (
					item.score === 0 &&
					currentLength + item.length <= maxLength
				) {
					optimizedContent += item.sentence + ". ";
					currentLength += item.length + 2;
				}
			}
		}

		return optimizedContent.trim();
	}

	/**
	 * Validate and sanitize business intelligence data
	 */
	private validateBusinessIntelligence(data: any): BusinessIntelligence {
		return {
			companyType: data.companyType || "unknown",
			industry: Array.isArray(data.industry) ? data.industry : [],
			businessModel: data.businessModel || "unknown",
			targetMarket: Array.isArray(data.targetMarket)
				? data.targetMarket
				: [],
			keyServices: Array.isArray(data.keyServices)
				? data.keyServices
				: [],
			competitiveAdvantages: Array.isArray(data.competitiveAdvantages)
				? data.competitiveAdvantages
				: [],
			marketPosition: [
				"startup",
				"small",
				"medium",
				"large",
				"enterprise",
			].includes(data.marketPosition)
				? data.marketPosition
				: "unknown",
			fundingStage: data.fundingStage || null,
			revenue: data.revenue || null,
			employeeCount: data.employeeCount || null,
			technologies: Array.isArray(data.technologies)
				? data.technologies
				: [],
			partnerships: Array.isArray(data.partnerships)
				? data.partnerships
				: [],
			certifications: Array.isArray(data.certifications)
				? data.certifications
				: [],
			awards: Array.isArray(data.awards) ? data.awards : [],
			socialPresence: {
				platforms: Array.isArray(data.socialPresence?.platforms)
					? data.socialPresence.platforms
					: [],
				engagement: ["low", "medium", "high"].includes(
					data.socialPresence?.engagement
				)
					? data.socialPresence.engagement
					: "unknown",
			},
			riskFactors: Array.isArray(data.riskFactors)
				? data.riskFactors
				: [],
			opportunities: Array.isArray(data.opportunities)
				? data.opportunities
				: [],
			confidence:
				typeof data.confidence === "number"
					? Math.max(0, Math.min(100, data.confidence))
					: 0,
		};
	}

	/**
	 * Get default business intelligence structure
	 */
	private getDefaultBusinessIntelligence(): BusinessIntelligence {
		return {
			companyType: "unknown",
			industry: [],
			businessModel: "unknown",
			targetMarket: [],
			keyServices: [],
			competitiveAdvantages: [],
			marketPosition: "unknown",
			technologies: [],
			partnerships: [],
			certifications: [],
			awards: [],
			socialPresence: { platforms: [], engagement: "unknown" },
			riskFactors: [],
			opportunities: [],
			confidence: 0,
		};
	}

	/**
	 * Get default contact info structure
	 */
	private getDefaultContactInfo(): EnhancedContactInfo {
		return {
			emails: [],
			phones: [],
			addresses: [],
			socialMedia: [],
			website: { primary: "", additional: [] },
		};
	}
}

// Export singleton instance
export const aiService = AIService.getInstance();
export default aiService;
