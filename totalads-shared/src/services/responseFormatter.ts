/**
 * Response Formatter Service
 * Cleans, deduplicates, and formats scraper responses to look professional and human-like
 */

export interface FormattedScrapeResponse {
	success: boolean;
	data: {
		// Core business information
		companyName: string;
		description: string;
		website: string;
		contentSummary: string;
		keyLinks: string[];

		// Business intelligence
		industry: string[];
		businessModel: string;
		targetMarket: string[];
		keyServices: string[];
		competitiveAdvantages: string[];

		// Contact information
		contactInfo: {
			emails: Array<{ address: string; type: string }>;
			phones: Array<{ number: string; type: string }>;
			addresses: Array<{ address: string; type: string }>;
			socialMedia: Array<{ platform: string; url: string }>;
		};

		// Company details
		companyDetails: {
			marketPosition: string;
			fundingStage: string;
			revenue: string;
			employeeCount: string;
			technologies: string[];
			partnerships: string[];
			awards: string[];
			certifications: string[];
		};

		// Additional insights
		businessInsights: {
			riskFactors: string[];
			opportunities: string[];
			sentiment: string;
			confidence: number;
		};

		// Metadata
		extractedAt: string;
		processingTime: number;
		dataQuality: "high" | "medium" | "low";
	};
	meta: {
		aiEnhanced: boolean;
		processingTime: number;
		aiCost: number;
	};
}

export class ResponseFormatterService {
	private static instance: ResponseFormatterService;

	private constructor() {}

	public static getInstance(): ResponseFormatterService {
		if (!ResponseFormatterService.instance) {
			ResponseFormatterService.instance = new ResponseFormatterService();
		}
		return ResponseFormatterService.instance;
	}

	/**
	 * Format and clean the scraper response to look professional
	 */
	formatResponse(
		basicResult: any,
		aiResult?: any,
		processingTime?: number,
		aiCost?: number
	): FormattedScrapeResponse {
		const companyName = this.extractCompanyName(
			basicResult.title,
			basicResult.url
		);
		const description = this.createCompanyDescription(
			basicResult,
			aiResult
		);

		return {
			success: true,
			data: {
				companyName,
				description,
				website: this.extractPrimaryWebsite(basicResult.url),
				contentSummary: this.summarizeContent(basicResult.text || ""),
				keyLinks: this.cleanLinks(basicResult.nestedLinks || []),

				industry: this.cleanArray(
					aiResult?.businessIntelligence?.industry || []
				),
				businessModel:
					aiResult?.businessIntelligence?.businessModel ||
					"Not specified",
				targetMarket: this.cleanArray(
					aiResult?.businessIntelligence?.targetMarket || []
				),
				keyServices: this.cleanArray(
					aiResult?.businessIntelligence?.keyServices || []
				),
				competitiveAdvantages: this.cleanArray(
					aiResult?.businessIntelligence?.competitiveAdvantages || []
				),

				contactInfo: this.formatContactInfo(
					basicResult.contactDetails,
					aiResult?.enhancedContactInfo
				),

				companyDetails: {
					marketPosition:
						aiResult?.businessIntelligence?.marketPosition ||
						"Not specified",
					fundingStage:
						aiResult?.businessIntelligence?.fundingStage ||
						"Not specified",
					revenue:
						aiResult?.businessIntelligence?.revenue ||
						"Not specified",
					employeeCount:
						aiResult?.businessIntelligence?.employeeCount ||
						"Not specified",
					technologies: this.cleanArray(
						aiResult?.businessIntelligence?.technologies || []
					),
					partnerships: this.cleanArray(
						aiResult?.businessIntelligence?.partnerships || []
					),
					awards: this.cleanArray(
						aiResult?.businessIntelligence?.awards || []
					),
					certifications: this.cleanArray(
						aiResult?.businessIntelligence?.certifications || []
					),
				},

				businessInsights: {
					riskFactors: this.cleanArray(
						aiResult?.businessIntelligence?.riskFactors || []
					),
					opportunities: this.cleanArray(
						aiResult?.businessIntelligence?.opportunities || []
					),
					sentiment: aiResult?.sentiment?.overall || "neutral",
					confidence: aiResult?.businessIntelligence?.confidence || 0,
				},

				extractedAt: new Date().toISOString(),
				processingTime: processingTime || 0,
				dataQuality: this.assessDataQuality(aiResult),
			},
			meta: {
				aiEnhanced: !!aiResult,
				processingTime: processingTime || 0,
				aiCost: aiCost || 0,
			},
		};
	}

	/**
	 * Extract company name from title and URL
	 */
	private extractCompanyName(title: string, url: string): string {
		if (!title) {
			// Extract from URL as fallback
			const domain = new URL(url).hostname.replace("www.", "");
			return (
				domain.split(".")[0].charAt(0).toUpperCase() +
				domain.split(".")[0].slice(1)
			);
		}

		// Clean up title to extract company name
		const cleanTitle = title
			.replace(/\s*[-|:]\s*.*/g, "") // Remove everything after dash or colon
			.replace(/\s*\|\s*.*/g, "") // Remove everything after pipe
			.trim();

		return cleanTitle || "Company Name Not Found";
	}

	/**
	 * Create a concise company description
	 */
	private createCompanyDescription(basicResult: any, aiResult?: any): string {
		// Try to get description from meta description first
		if (basicResult.desc && basicResult.desc.length > 20) {
			return this.cleanDescription(basicResult.desc);
		}

		// Try to extract from AI business intelligence
		if (aiResult?.businessIntelligence?.keyServices?.length > 0) {
			const services = aiResult.businessIntelligence.keyServices
				.slice(0, 3)
				.join(", ");
			const industry =
				aiResult.businessIntelligence.industry?.[0] || "business";
			return `A ${industry} company specializing in ${services}.`;
		}

		return "Business description not available";
	}

	/**
	 * Clean and format contact information
	 */
	private formatContactInfo(basicContact: any, enhancedContact?: any) {
		const result = {
			emails: [] as Array<{ address: string; type: string }>,
			phones: [] as Array<{ number: string; type: string }>,
			addresses: [] as Array<{ address: string; type: string }>,
			socialMedia: [] as Array<{ platform: string; url: string }>,
		};

		// Process enhanced contact info if available
		if (enhancedContact) {
			result.emails = this.deduplicateContacts(
				enhancedContact.emails || [],
				"address"
			);
			result.phones = this.deduplicateContacts(
				enhancedContact.phones || [],
				"number"
			);
			result.addresses = this.deduplicateContacts(
				enhancedContact.addresses || [],
				"address"
			);
			result.socialMedia = this.deduplicateContacts(
				enhancedContact.socialMedia || [],
				"url"
			);
		}

		// Fallback to basic contact if enhanced is empty
		if (result.emails.length === 0 && basicContact?.email?.length > 0) {
			result.emails = basicContact.email.map((email: string) => ({
				address: email,
				type: "general",
			}));
		}

		if (result.phones.length === 0 && basicContact?.phone?.length > 0) {
			result.phones = basicContact.phone.map((phone: string) => ({
				number: phone,
				type: "main",
			}));
		}

		return result;
	}

	/**
	 * Clean array by removing duplicates, empty values, and generic entries
	 */
	private cleanArray(arr: string[]): string[] {
		if (!Array.isArray(arr)) return [];

		return arr
			.filter(
				(item) =>
					item && typeof item === "string" && item.trim().length > 0
			)
			.filter((item) => !this.isGenericValue(item))
			.map((item) => item.trim())
			.filter((item, index, self) => self.indexOf(item) === index) // Remove duplicates
			.slice(0, 10); // Limit to 10 items max
	}

	/**
	 * Check if a value is too generic to be useful
	 */
	private isGenericValue(value: string): boolean {
		const genericValues = [
			"unknown",
			"not specified",
			"not available",
			"n/a",
			"tbd",
			"coming soon",
			"various",
			"multiple",
			"different",
			"all",
			"many",
			"several",
		];

		return genericValues.includes(value.toLowerCase()) || value.length < 3;
	}

	/**
	 * Clean and deduplicate links, keeping only important ones
	 */
	private cleanLinks(links: string[]): string[] {
		if (!Array.isArray(links)) return [];

		// Important link patterns to prioritize
		const importantPatterns = [
			"/about",
			"/contact",
			"/team",
			"/careers",
			"/pricing",
			"/features",
			"/services",
			"/products",
			"/blog",
			"/news",
			"/investors",
		];

		const cleanedLinks = links
			.filter((link) => link && typeof link === "string")
			.filter((link, index, self) => self.indexOf(link) === index) // Remove duplicates
			.filter((link) => {
				try {
					new URL(link); // Validate URL
					return true;
				} catch {
					return false;
				}
			});

		// Prioritize important links
		const importantLinks = cleanedLinks.filter((link) =>
			importantPatterns.some((pattern) =>
				link.toLowerCase().includes(pattern)
			)
		);

		// Add other unique links up to a limit
		const otherLinks = cleanedLinks
			.filter((link) => !importantLinks.includes(link))
			.slice(0, 10 - importantLinks.length);

		return [...importantLinks, ...otherLinks].slice(0, 15);
	}

	/**
	 * Remove duplicate contacts based on a key field
	 */
	private deduplicateContacts<T extends Record<string, any>>(
		contacts: T[],
		keyField: keyof T
	): T[] {
		const seen = new Set();
		return contacts.filter((contact) => {
			const key = contact[keyField];
			if (seen.has(key)) return false;
			seen.add(key);
			return true;
		});
	}

	/**
	 * Clean description text
	 */
	private cleanDescription(desc: string): string {
		return (
			desc
				.replace(/\s+/g, " ") // Replace multiple spaces with single space
				.replace(/[^\w\s.,!?-]/g, "") // Remove special characters except basic punctuation
				.trim()
				.slice(0, 200) + // Limit length
			(desc.length > 200 ? "..." : "")
		);
	}

	/**
	 * Summarize long content into key business points
	 */
	private summarizeContent(content: string): string {
		if (!content || content.length < 500) return content;

		// Extract key sentences that contain business-relevant keywords
		const businessKeywords = [
			"company",
			"business",
			"service",
			"product",
			"solution",
			"client",
			"customer",
			"industry",
			"market",
			"technology",
			"platform",
			"software",
			"enterprise",
			"founded",
			"headquarters",
			"office",
			"team",
			"mission",
			"vision",
		];

		const sentences = content
			.split(/[.!?]+/)
			.filter((s) => s.trim().length > 20);
		const relevantSentences = sentences
			.filter((sentence) =>
				businessKeywords.some((keyword) =>
					sentence.toLowerCase().includes(keyword)
				)
			)
			.slice(0, 5); // Take top 5 relevant sentences

		if (relevantSentences.length === 0) {
			// Fallback to first few sentences if no business keywords found
			return sentences.slice(0, 3).join(". ").trim() + ".";
		}

		return relevantSentences.join(". ").trim() + ".";
	}

	/**
	 * Extract primary website from URL
	 */
	private extractPrimaryWebsite(url: string): string {
		try {
			const urlObj = new URL(url);
			return `${urlObj.protocol}//${urlObj.hostname}`;
		} catch {
			return url;
		}
	}

	/**
	 * Assess data quality based on completeness and AI confidence
	 */
	private assessDataQuality(aiResult?: any): "high" | "medium" | "low" {
		if (!aiResult) return "low";

		const confidence = aiResult.businessIntelligence?.confidence || 0;
		const hasKeyData =
			aiResult.businessIntelligence?.industry?.length > 0 &&
			aiResult.businessIntelligence?.keyServices?.length > 0;

		if (confidence >= 80 && hasKeyData) return "high";
		if (confidence >= 60 || hasKeyData) return "medium";
		return "low";
	}
}

export const responseFormatter = ResponseFormatterService.getInstance();
